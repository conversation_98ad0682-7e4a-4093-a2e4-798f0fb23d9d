import { Fragment, useState, useCallback } from 'react'
import { View, Text } from '@tarojs/components'
import { withPage, DateSelect, ListView, usePagination, FormTip, BottomBtn } from '@components'
import Buttons, { ButtonIconType } from '@components/buttons'
import Taro from '@tarojs/taro'
import { ncmp } from '@apis/ncmp'
import { getScrollStyle } from '@utils/transforms'
import { getGlobalData } from '@utils'
import dayjs from 'dayjs'
import styles from './index.module.scss'


// 业务状态枚举
enum BusinessStatus {
  PROCESSING = '1',
  COMPLETED = '2'
}

// 配置常量
const ITEM_CONFIG = [
  { title: '业务类型', key: 'categoryName' as keyof defs.pact.BusinessListData },
  { title: '业务项目', key: 'busnameClassName' as keyof defs.pact.BusinessListData },
  { title: '业务内容', key: 'busContent' as keyof defs.pact.BusinessListData },
  { title: '办理开始时间', key: 'createDt' as keyof defs.pact.BusinessListData },
  { title: '办理状态', key: 'businessStatus' as keyof defs.pact.BusinessListData }
] as const

const BUSINESS_STATUS_MAP = new Map<string, string>([
  [BusinessStatus.PROCESSING, '办理中'],
  [BusinessStatus.COMPLETED, '办理完成']
])

// 默认日期范围：最近6个月（与DateSelect组件默认选择保持一致）
const DEFAULT_DATE_RANGE = {
  startDate: dayjs()
    .subtract(6, 'months')
    .add(1, 'days')
    .format('YYYY-MM-DD'),
  endDate: dayjs().format('YYYY-MM-DD')
}

const Index = () => {
  const scrollStyle = getScrollStyle({ top: 256 + 60 })
  const { empId } = getGlobalData<'account'>('account')

  // 日期范围状态：存储开始和结束日期
  const [dateRange, setDateRange] = useState({
    startDate: DEFAULT_DATE_RANGE.startDate,
    endDate: DEFAULT_DATE_RANGE.endDate
  })

  // 业务列表请求函数
  const fetchBusinessList = useCallback(
    async (page: number) => {
      try {
        const result = await ncmp.ebmBusiness.selectEbmBusinessPage.request({
          startDt: dateRange.startDate,
          endDt: dateRange.endDate,
          pageNo: String(page),
          empId,
        })
        // 数据验证
        if (!result?.resultObj || !Array.isArray(result.resultObj)) {
          console.warn('Invalid business list data:', result)
          return []
        }
        return result.resultObj
      } catch (error) {
        console.error('Failed to fetch business list:', error)
        Taro.showToast({
          title: '获取业务列表失败，请稍后重试',
          icon: 'none'
        })
        return []
      }
    },
    [dateRange.startDate, dateRange.endDate, empId]
  )

  const list = usePagination(fetchBusinessList, { deps: [dateRange] })

  // 跳转到业务详情页
  const handleViewDetail = useCallback((businessId: string) => {
    if (!businessId) {
      Taro.showToast({ title: '业务ID无效', icon: 'none' })
      return
    }

    Taro.navigateTo({
      url: `/pages/appointment/business-detail/index?businessId=${businessId}`
    })
  }, [])

  // 数据格式化函数
  const formatValue = useCallback((key: string, value: string | undefined) => {
    if (!value) return '-'

    switch (key) {
      case 'businessStatus':
        return BUSINESS_STATUS_MAP.get(value) || value
      case 'createDt':
        return value.length >= 10 ? value.substring(0, 10) : value
      default:
        return value
    }
  }, [])

  // 日期选择处理
  const handleDateSelect = useCallback((selectedDateRange: { startDate: string; endDate: string }) => {
    // 优化当选择不同 ’最近三个月', '最近六个月', '最近一年', '全部‘ 时, 接口参数 startDt， endDt 变为选中当前日期时间段
    setDateRange({
      startDate: selectedDateRange.startDate,
      endDate: selectedDateRange.endDate
    })
  }, [])

  // 渲染列表项
  const renderItem = useCallback(
    (item: defs.pact.BusinessListData, _index: number, id: string) => (
      <View className={styles.item} id={id}>
        {ITEM_CONFIG.map(config => (
          <View className={styles.text_wrap} key={config.key}>
            <Text className={styles.title}>{config.title}：</Text>
            <Text className={styles.detail}>{formatValue(config.key, item[config.key])}</Text>
          </View>
        ))}
        <View className={styles.btn_wrap}>
          <Buttons title='提交材料' icon={ButtonIconType.ORDER} />
          <Buttons
            title='查看明细'
            icon={ButtonIconType.DETAIL}
            onClick={() => handleViewDetail(item.businessId || '')}
          />
        </View>
      </View>
    ),
    [formatValue, handleViewDetail]
  )
  
  return (
    <Fragment>
      <DateSelect onSelectHandle={handleDateSelect} pickerProps={[{ fields: 'day' }, { fields: 'day' }]} />
      <View className={styles.tip}>
        <FormTip tip='提示：查询时间为提交时间' />
      </View>
      <ListView style={scrollStyle} itemSize={438} unlimitedSize renderItem={renderItem} {...list} />
      <BottomBtn
        btns={[{ title: '新增业务办理', onClick: () => Taro.navigateTo({ url: '/pages/appointment/add-business/index' }) }]}
      />
    </Fragment>
  )
}

export default withPage(Index)
