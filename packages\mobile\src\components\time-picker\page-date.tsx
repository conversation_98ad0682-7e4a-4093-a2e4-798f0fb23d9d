import { useState, FunctionComponent, useEffect } from 'react'
import { View, Text } from '@tarojs/components'
import { dynamicStyle } from '@utils'
import dayjs from 'dayjs'
import { DateRangePicker } from './date-range'
import styles from './index.module.scss'
import { stdDateFormat, TimePickerProps } from '.'

interface DateSelectProps {
  onSelectHandle: ({}: any) => void
  pickerProps?: [TimePickerProps, TimePickerProps]
  showData?: any
  sendMonth?:string
}

const DateSelect: FunctionComponent<DateSelectProps> = props => {
  const { onSelectHandle, pickerProps } = props
  const [active, setActive] = useState<string>('2') // 默认选择最近六个月
  const [values, setValues] = useState<[string , string]>()
  const std = pickerProps?.[0]?.name || 'startDate'
  const end = pickerProps?.[0]?.name || 'endDate'
  const search = (key:string) => {
    setActive(key)
    let startDate = ''
    let endDate = ''
    endDate = dayjs().format(stdDateFormat)
    if (key === '1') {
      startDate = dayjs()
        .subtract(3, 'months')
        .add(1, 'days')
        .format(stdDateFormat)
    } else if (key === '2') {
      startDate = dayjs()
        .subtract(6, 'months')
        .add(1, 'days')
        .format(stdDateFormat)
    } else if (key === '3') {
      startDate = dayjs()
        .subtract(12, 'months')
        .add(1, 'days')
        .format(stdDateFormat)
    } else if (key === '4') {
      // 全部：设置一个较大的日期范围，比如从5年前到今天
      startDate = dayjs()
        .subtract(5, 'years')
        .format(stdDateFormat)
      endDate = dayjs().format(stdDateFormat)
    }
    setValues([startDate, endDate])
    onSelectHandle &&
      onSelectHandle({
        [std]: startDate,
        [end]: endDate
      })
  }
  useEffect(() => {
    // 初始化时只设置默认值，不触发回调
    const startDate = dayjs()
      .subtract(6, 'months')
      .add(1, 'days')
      .format(stdDateFormat)
    const endDate = dayjs().format(stdDateFormat)
    setValues([startDate, endDate])
  }, [])
  return (
    <DateRangePicker
      onSearchHandle={onSelectHandle}
      pickerProps={[
        { value: values?.[0], ...pickerProps?.[0] },
        { value: values?.[1], ...pickerProps?.[1] }
      ]}
    >
      <View className={styles.tag_wrap}>
        {['最近三个月', '最近六个月', '最近一年', '全部'].map((item, index) => (
          <View
            key={index}
            {...dynamicStyle(active === String(index + 1) ? styles.tag_btn_active : styles.tag_btn)}
            onClick={() => search(String(index + 1))}
          >
            <Text {...dynamicStyle(active === String(index + 1) ? styles.btn_text_active : styles.btn_text)}>
              {item}
            </Text>
          </View>
        ))}
      </View>
    </DateRangePicker>
  )
}

const DateYearMonth = (props: DateSelectProps) => {
  const { onSelectHandle, pickerProps, showData } = props
  return (
    <DateRangePicker onSearchHandle={onSelectHandle} pickerProps={pickerProps}>
      <View className={styles.group_query}>
        <View className={styles.list_top_item}>
          <Text className={styles.list_top_item_title}>企业金额合计</Text>
          <Text className={styles.list_top_item_text}>{showData?.eTotalAmt}</Text>
        </View>
        <View className={styles.list_top_item}>
          <Text className={styles.list_top_item_title}>个人金额合计</Text>
          <Text className={styles.list_top_item_text}>{showData?.pTotalAmt}</Text>
        </View>
      </View>
    </DateRangePicker>
  )
}

const DateYearMonthWage = (props: DateSelectProps) => {
  const { onSelectHandle, pickerProps, showData, sendMonth} = props
  return (
    <DateRangePicker onSearchHandle={onSelectHandle} pickerProps={pickerProps} bgType={2} sendMonth={sendMonth}>
      <View className={styles.group_query}>
        <View className={styles.group_list}>
          <View className={styles.list_top_item}>
            <Text className={styles.list_top_item_title}>收入合计</Text>
            <Text className={styles.list_top_item_text}>{showData?.f1}</Text>
          </View>
          <View className={styles.list_top_item}>
            <Text className={styles.list_top_item_title}>扣款合计</Text>
            <Text className={styles.list_top_item_text}>{showData?.f2}</Text>
          </View>
        </View>
        <View className={styles.group_list}>
          <View className={styles.list_top_item}>
            <Text className={styles.list_top_item_title}>实发合计</Text>
            <Text className={styles.list_top_item_text}>{showData?.f3}</Text>
          </View>
          <View className={styles.list_top_item}>
            <Text className={styles.list_top_item_title}>本次扣税</Text>
            <Text className={styles.list_top_item_text}>{showData?.f10}</Text>
          </View>
        </View>
      </View>
    </DateRangePicker>
  )
}

const DateYearMonthSalary = (props: DateSelectProps) => {
  const { onSelectHandle, pickerProps, showData } = props
  return (
    <DateRangePicker onSearchHandle={onSelectHandle} pickerProps={pickerProps} bgType={2}>
      <View className={styles.group_query}>
        <View className={styles.group_list}>
          <View className={styles.list_top_item}>
            <Text className={styles.list_top_item_title}>工资薪金合计</Text>
            <Text className={styles.list_top_item_text}>{showData?.f9}</Text>
          </View>
          <View className={styles.list_top_item}>
            <Text className={styles.list_top_item_title}>劳务合计</Text>
            <Text className={styles.list_top_item_text}>{showData?.f33}</Text>
          </View>
        </View>
        <View className={styles.group_list}>
          <View className={styles.list_top_item}>
            <Text className={styles.list_top_item_title}>年终奖合计</Text>
            <Text className={styles.list_top_item_text}>{showData?.f18}</Text>
          </View>
        </View>
      </View>
    </DateRangePicker>
  )
}

export {
  // 同vue员工预约办理页面
  DateSelect,
  // 同vue个人社保查询页面
  DateYearMonth,
  // 同vue工资及完税情况查询页面
  DateYearMonthWage,
  // 同vue薪资档案查询
  DateYearMonthSalary
}
