import { Fragment, useEffect, useState, useRef, useCallback } from 'react'
import { View, Text } from '@tarojs/components'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { Form, BottomBtn, FormItemProps, withPage, useForm, FormTip, FormLabel, SimpleMaterialUpload } from '@components'
import { getScrollStyle } from '@utils/transforms'
import { pact } from '@apis/pact'
import { getGlobalData, getGuid, navigateTo } from '@utils'
import type { MaterialInfo, FileInfo } from '@components/simple-material-upload'
import styles from './index.module.scss'

type AccountInfo = { idCardNum?: string; mobilePhoneNum?: string; empName?: string }
type options = { key: string; value: string }[]
const Options: options = [
  { key: '1', value: '社保业务' },
  { key: '2', value: '公积金业务' },
  { key: '3', value: '人力资源收费服务' },
  { key: '4', value: '定点医疗机构变更' }
]
const Index = () => {
  const [company, setCompany] = useState<defs.pact.CompanyResponseData>()
  const [things, setThings] = useState<any[]>()
  const [busTypes, setBusTypes] = useState<any[]>()
  const [accountInfo, setAccountInfo] = useState<AccountInfo>()
  const [materials, setMaterials] = useState<defs.pact.MaterData[]>([])
  const [materialFiles, setMaterialFiles] = useState<Record<string, FileInfo[]>>({})
  const companyOptionsRef = useRef<options>([])
  const { openId, accountId, empId } = getGlobalData<'account'>('account')
  const { categoryName, businessName } = useRouter().params
  const [uuid] = useState(getGuid())
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const form = useForm()
  // 获取材料列表
  const getMaterialList = useCallback((subTypeId: string) => {
    if (!subTypeId) return

    pact.appoint.getMaterialList
      .request({
        subTypeId,
        openId
      })
      .then(res => {
        if (res.code === '200' && res.data) {
          setMaterials(res.data)
        } else {
          Taro.showToast({ icon: 'none', title: '获取材料列表失败' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  }, [openId])

  // 处理文件变化
  const handleFileChange = (materialId: string, files: FileInfo[]) => {
    setMaterialFiles(prev => ({
      ...prev,
      [materialId]: files
    }))
  }

  const onSubmit = (values: any) => {
    values.accountInfo.mobilePhoneNum = values?.mobilePhoneNum
    pact.appoint.appointment
      .request({
        ...values,
        busSubTypeIdStr: values?.busSubtypeId,
        cancelReason: '',
        uuid,
        busSubTypeIdStrName: undefined,
        createBy: empId,
        bookingRemark: values.bookingRemark?.replace(/[\r\n]/g, ''),
        openId
      })
      .then(res => {
        if (res.code == '200') {
          Taro.navigateBack()
          navigateTo('/appointment/success')
        } else {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  }
  useDidShow(() => {
    pact.per.personInformation
      .request({ accountId, openId })
      .then(res => {
        const { idCardNum, mobilePhoneNum, empName } = res.data || {}
        const _accountInfo = { idCardNum, mobilePhoneNum, empName }
        setAccountInfo(_accountInfo)
        form.setValue('accountInfo', _accountInfo)
        form.setValue('mobilePhoneNum', mobilePhoneNum)
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  })
  const cityId = form.watch('cityId')
  const categoryId = form.watch('categoryId')
  const bussNameClassId = form.watch('bussNameClassId')
  const busSubtypeId = form.watch('busSubtypeId')

  // 监听业务小类变化，获取材料列表
  useEffect(() => {
    if (busSubtypeId) {
      getMaterialList(busSubtypeId)
    } else {
      setMaterials([])
      setMaterialFiles({})
    }
  }, [busSubtypeId])

  const columns: FormItemProps[] = [
    {
      showLine: false,
      render: () => <FormLabel level={2} title='业务信息' />
    },
    {
      name: 'cityId',
      type: 'page_choice',
      title: '城市',
      rules: { required: true },
      pageOptions: {
        keys: ['cityId', 'cityName'],
        labelKey: 'cityName',
        url: '/policy-city'
      }
    },
    {
      isHidden: !company?.name,
      render: () => <View className={styles.company_name}>{company?.name}</View>
    },
    {
      isHidden: !company?.address,
      render: () => (
        <View>
          <View className={styles.company_title}>分公司联系信息</View>
          <View className={styles.company_name}>公司地址：{company?.address}</View>
          <View className={styles.company_name}>联系电话：{company?.contactTel}</View>
        </View>
      )
    },
    {
      title: '业务类型',
      name: 'categoryId',
      type: 'select',
      rules: { required: true },
      options: companyOptionsRef.current,
      disabled: !cityId,
      onClick: () => {
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
        }
      }
    },
    {
      title: '业务项目',
      name: 'busnameClassId',
      type: 'select',
      rules: { required: true },
      options: things,
      disabled: !cityId || !categoryId,
      onClick: () => {
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
        }
        if (!categoryId) {
          Taro.showToast({ title: '请选择所属类型' })
        }
      }
    },
    {
      title: '业务内容',
      name: 'busContent',
      type: 'select',
      rules: { required: true },
      options: busTypes,
      disabled: !cityId || !categoryId || !bussNameClassId || busTypes?.length === 0,
      onClick: () => {
        if (busTypes?.length === 0) {
          Taro.showToast({ title: '暂无可选信息！' })
          return
        }
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
          return
        }
        if (!categoryId) {
          Taro.showToast({ title: '请选择所属类型' })
          return
        }
        if (!bussNameClassId) {
          Taro.showToast({ title: '请选择业务项目' })
          return
        }
      }
    },
    {
      title: '预约人姓名',
      rules: { required: true },
      render: () => <Text className={styles.company_name}>{accountInfo?.empName}</Text>
    },
    {
      title: '身份证号',
      rules: { required: true },
      render: () => <Text className={styles.company_name}>{accountInfo?.idCardNum}</Text>
    },
    {
      title: '手机号码',
      type: 'mobile',
      rules: { required: true },
      name: 'mobilePhoneNum'
    },
    // 若所选业务内容为 流程业务，此字段隐藏不显示
    {
      showLine: false,
      render: () => <FormLabel level={2} title='材料列表' />,
      isHidden: materials.length === 0
    },
    // 若所选业务内容为 流程业务，此字段隐藏不显示
    {
      showLine: false,
      isHidden: materials.length === 0,
      render: () => (
        <View className={styles.materialSection}>
                <SimpleMaterialUpload
                  material={{
                    materialsId: '002',
                    materialsName: '合同',
                    isOriginal: '0',
                    materialsAccount: 1,
                    isReturn: '0',
                    isRequired: true
                  }}
                  files={[]}
                  uuid={uuid}
                  onFileChange={() => {}}
                  onUploadSuccess={(file) => {
                    console.log('合同上传成功:', file)
                  }}
                  onUploadError={(error) => {
                    console.error('合同上传失败:', error)
                    // 这里可以添加额外的错误处理逻辑，比如记录日志等
                  }}
                />
          {/* {materials.map((material, index) => (
            <SimpleMaterialUpload
              key={material.materialsInfo?.materialsId || index}
              material={{
                materialsId: material.materialsInfo?.materialsId || '',
                materialsName: material.materialsInfo?.materialsName || '',
                isOriginal: material.materialsInfo?.isOriginal || '0',
                materialsAccount: material.materialsAccount || (index + 1),
                isReturn: material.isReturn?.toString() || '0',
                isRequired: true // 可以根据业务逻辑调整
              }}
              files={materialFiles[material.materialsInfo?.materialsId || ''] || []}
              uuid={uuid}
              onFileChange={(files) => handleFileChange(material.materialsInfo?.materialsId || '', files)}
              onUploadSuccess={(file) => {
                console.log('上传成功:', file)
              }}
              onUploadError={(error) => {
                console.error('上传失败:', error)
              }}
            />
          ))} */}
        </View>
      )
    },
    {
      render: () => <FormTip tip=' 提示: 上述材料仅供参考，实际提交材料以易才管家推送为准。' />,
      isHidden: materials.length === 0
    },
    {
      title: '员工自助办理途径',
      type: 'textarea'
    },
    {
      title: '是否自助办理',
      type: 'select'
    },
    {
      title: '预约备注',
      type: 'textarea',
      name: 'bookingRemark'
    }
  ]
  return (
    <Fragment>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '提交',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </Fragment>
  )
}

export default withPage(Index)
